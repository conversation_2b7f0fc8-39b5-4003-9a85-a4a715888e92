# Loan表状态卡在INIT问题分析报告

## 📋 问题描述

Flow系统中loan表记录一直卡在INIT状态，并且loan_record表没有对应的数据记录。

## 🔍 具体案例分析

**最新日志信息：**
```
LoanCommonService.limitSuspend:307 - limitSuspend: [LO250810103400094456333756861021], 资方:CYBK, 剩余额度为:18866506.00, 放款金额为:10000, 额度充足,允许放款
```

**关键发现：**
1. **LoanId**: `LO250810103400094456333756861021`
2. **资方**: `CYBK` (创业银行)
3. **额度状态**: 充足 (剩余18866506.00，需要10000)
4. **limitSuspend结果**: 返回`RECOVER`，允许放款

**问题定位：**
从日志可以看出，`limitSuspend`方法已经正常执行并返回`RECOVER`状态，说明额度检查通过。但loan状态仍然卡在INIT，说明问题出现在`limitSuspend`之后的流程中。

## 🔍 问题分析

### 1. 正常流程分析

根据代码分析，正常的loan状态流转应该是：

```
INIT → PROCESSING → SUCCESS/FAILED
```

**关键流程节点：**

1. **LoanService.apply()** - 创建loan记录，状态为INIT
2. **mqService.submitLoanApply()** - 发送MQ消息
3. **LoanApplyListener.loanApplyListen()** - 监听MQ消息
4. **LoanService.loanApply()** - 处理放款申请
5. **LoanService.genLoanRecord()** - 生成loan_record记录
6. **mqService.submitLoanRecordApply()** - 发送loan_record处理消息
7. **LoanRecordApplyListener** - 处理具体放款逻辑

### 2. 可能的卡住原因

#### 2.1 suspendCheck后续流程问题 ⭐ **重点排查**

**根据日志分析，问题很可能出现在以下环节：**

**环节1：timeSuspend时间校验**
```java
// LoanCommonService.java 第209行
if (timeSuspend(loan.getBankChannel())) {
    return LoanSuspendFlag.SUSPEND;
}
```
- CYBK资方可能配置了放款时间限制
- 当前时间不在允许放款的时间窗口内

**环节2：creditSuspend授信有效期校验**
```java
// LoanCommonService.java 第218行
if (creditSuspend(loan)) {
    return LoanSuspendFlag.SUSPEND;
}
```
- 虽然当前实现返回false，但可能有其他逻辑

**环节3：MQ消息发送失败**
```java
// LoanService.java 第180行
mqService.submitLoanApply(loan.getId());
```
- MQ服务异常导致消息发送失败
- 消息发送成功但未被消费

#### 2.2 MQ消息处理异常

**问题点1：MQ消息未被消费**
- MQ服务可能未正常运行
- 队列配置错误或队列不存在
- 消息路由配置问题

**问题点2：消息处理异常**
```java
// LoanApplyListener.java 第41行
getMqWarningService().warn("放款申请异常:loanId," + loanId + "," + e.getMessage(), msg -> logger.error(msg, e));
```
- 异常被捕获但未重试
- 超过最大重试次数后停止处理

#### 2.2 业务逻辑校验失败

**挂起检查失败：**
```java
// LoanService.java 第160-175行
LoanSuspendFlag loanSuspendFlag = loanCommonService.suspendCheck(loan);
switch (loanSuspendFlag) {
    case IGNORE -> { return loan; }
    case SUSPEND -> {
        loan.setLoanState(ProcessState.SUSPEND);
        return loanRepository.save(loan);
    }
}
```

**可能的挂起原因：**
- 时间限制校验失败 (`timeSuspend()`)
- 额度限制校验失败 (`limitSuspend()`)
- 授信有效期校验失败 (`creditSuspend()`)

#### 2.3 数据库相关问题

**事务问题：**
- 数据库连接池耗尽
- 长事务导致锁等待
- 数据库死锁

**数据一致性问题：**
- loan记录存在但相关依赖数据缺失
- 外键约束问题

#### 2.4 前置条件检查失败

**流量三要素验证：**
```java
// LoanService.java 第154-158行
boolean isPass = preLoanService.flowPreLoanCheck(order);
if (!isPass) {
    loanCommonService.loanFail(loan);
    return loan;
}
```

**放款记录检查：**
```java
// LoanService.java 第189-192行
if (loanRecordRepository.existsByLoanIdAndLoanStateNotIn(loanId, ProcessState.FAILED)) {
    warningService.warn("放款申请异常,借据[" + loanId + "]存在非失败的放款记录", logger::error);
    return;
}
```

## 🔧 排查步骤

### 1. 针对性排查步骤 ⭐ **优先执行**

**基于日志分析的重点排查：**

**步骤1：检查CYBK资方时间配置**
```sql
-- 检查CYBK资方的放款时间配置
SELECT bank_channel, loan_start_time, loan_end_time, enabled, updated_time
FROM capital_config
WHERE bank_channel = 'CYBK';
```

**步骤2：检查具体loan记录的完整日志**
```bash
# 搜索该loanId的完整处理日志
grep "LO250810103400094456333756861021" /home/<USER>/logs/cash-business/cash-business.log

# 搜索suspendCheck的完整结果
grep -A 5 -B 5 "挂起校验 suspendCheck" /home/<USER>/logs/cash-business/cash-business.log | grep -A 10 -B 10 "LO250810103400094456333756861021"
```

**步骤3：检查MQ消息发送情况**
```bash
# 搜索该loanId的MQ消息发送日志
grep -A 2 -B 2 "申请放款.*LO250810103400094456333756861021" /home/<USER>/logs/cash-business/cash-business.log
```

### 2. 检查MQ状态

```bash
# 检查RabbitMQ服务状态
systemctl status rabbitmq-server

# 检查队列状态
rabbitmqctl list_queues name messages consumers
```

**关键队列：**
- `loan.apply` - 放款申请队列
- `loan.record.apply` - 放款记录申请队列

### 3. 检查应用日志

**日志路径：**
- `/home/<USER>/logs/cash-business/cash-business.log`

**关键日志搜索：**
```bash
# 搜索放款申请相关日志
grep "监听放款申请" /home/<USER>/logs/cash-business/cash-business.log

# 搜索异常信息
grep "放款申请异常" /home/<USER>/logs/cash-business/cash-business.log

# 搜索挂起相关日志
grep "挂起校验" /home/<USER>/logs/cash-business/cash-business.log

# 搜索时间限制相关日志
grep "当前资方放款限制时间" /home/<USER>/logs/cash-business/cash-business.log
```

### 4. 数据库检查

```sql
-- 检查具体的loan记录
SELECT id, loan_state, apply_time, bank_channel, fail_reason, created_time, updated_time
FROM loan
WHERE id = 'LO250810103400094456333756861021';

-- 检查该loan对应的order状态
SELECT o.id, o.order_state, o.user_id, l.id as loan_id, l.loan_state
FROM `order` o
JOIN loan l ON o.id = l.order_id
WHERE l.id = 'LO250810103400094456333756861021';

-- 检查是否有对应的loan_record
SELECT lr.*
FROM loan_record lr
WHERE lr.loan_id = 'LO250810103400094456333756861021';

-- 检查CYBK资方配置详情
SELECT bank_channel, enabled, loan_day_limit, loan_start_time, loan_end_time, updated_time
FROM capital_config
WHERE bank_channel = 'CYBK';

-- 检查其他卡住的loan记录
SELECT id, loan_state, apply_time, bank_channel, fail_reason, created_time
FROM loan
WHERE loan_state = 'INIT'
ORDER BY created_time DESC
LIMIT 10;
```

### 5. 配置检查

**Apollo配置检查：**
- 数据库连接配置
- MQ连接配置
- 业务开关配置
- CYBK资方相关配置

## 🚨 常见解决方案

### 1. MQ问题解决

```bash
# 重启MQ服务
systemctl restart rabbitmq-server

# 清理死信队列
rabbitmqctl purge_queue loan.apply
```

### 2. 手动触发处理

```java
// 通过管理接口手动触发
@PostMapping("/manual/loan/apply/{loanId}")
public void manualLoanApply(@PathVariable String loanId) {
    loanService.loanApply(loanId);
}
```

### 3. 数据修复

```sql
-- 重置loan状态（谨慎操作）
UPDATE loan
SET loan_state = 'INIT', apply_time = NOW()
WHERE id = 'xxx' AND loan_state = 'INIT';
```

## 📊 监控建议

### 1. 添加监控指标

- loan表INIT状态记录数量
- MQ队列消息积压数量
- 放款申请异常次数

### 2. 告警规则

- INIT状态记录超过阈值时间告警
- MQ消息积压告警
- 放款申请异常频率告警

## 🔄 预防措施

1. **完善异常处理**：确保所有异常都有适当的重试机制
2. **增加监控**：实时监控关键业务指标
3. **定期巡检**：定期检查卡住的记录并处理
4. **优化配置**：调整MQ和数据库连接池配置
5. **业务开关**：增加业务降级开关，避免系统性问题

---

## 🎯 针对当前案例的建议

**基于日志分析的优先级排查：**

1. **🔥 最高优先级：检查CYBK时间限制**
   - 查看CYBK资方的`loan_start_time`和`loan_end_time`配置
   - 确认当前时间是否在允许放款的时间窗口内

2. **⚡ 高优先级：检查完整的suspendCheck日志**
   - 搜索该loanId的完整suspendCheck执行日志
   - 确认timeSuspend和creditSuspend的返回结果

3. **📋 中优先级：检查MQ消息处理**
   - 确认`mqService.submitLoanApply()`是否被正常调用
   - 检查MQ队列状态和消息消费情况

4. **🔍 低优先级：数据一致性检查**
   - 确认loan记录的状态和相关数据完整性

**预期结果：**
- 如果是时间限制问题，应该能看到"当前资方放款限制时间"的日志
- 如果是MQ问题，应该能看到消息发送或消费的异常日志
- 如果是其他业务逻辑问题，应该能在完整的suspendCheck日志中找到线索
