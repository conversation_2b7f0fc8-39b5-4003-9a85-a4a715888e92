# 内部撞库接口实现总结

## 实现完成的功能

### 1. 修改UserCheckRequest类
- 添加了`productType`字段用于确定资方渠道
- 保持了原有的`phoneMd5`和`idCardMd5`字段

### 2. 扩展UserInfoRepository
- 添加了`findCertNoByMd5`方法，用于根据身份证MD5查找原始身份证号
- 使用MySQL的MD5函数进行查询

### 3. 在RiskService中实现内部撞库逻辑
- 添加了`internalUserCheck`方法，实现完整的内部撞库流程
- 添加了`performInternalRiskCheck`方法，执行内部风控检查

### 4. 在LvxinUserController中添加新接口
- 新增`/api/partner/v1/internal-access`和`/api/partner/v2/internal-access`接口
- 复用现有的请求和响应结构

## 核心实现逻辑

### 内部撞库流程
```
1. 参数校验（身份证MD5不能为空）
   ↓
2. 获取资方渠道（通过productType转换）
   ↓
3. 根据MD5查找身份证号
   ├─ 找不到 → 新用户，直接通过
   └─ 找到 → 老用户，继续检验
       ↓
4. 30天内同渠道内部风控拒绝检验
   ↓ (通过)
5. 30天内同渠道同资方授信失败检验
   ↓ (通过)
6. 内部风控查询
   ↓ (通过)
7. 返回通过结果
```

### 关键方法说明

#### RiskService.internalUserCheck()
- 入参：`certNoMd5`, `productType`, `channel`
- 返回：`CollisionRecord`对象，包含检验结果和原因
- 异常处理：捕获所有异常，返回系统异常状态

#### RiskService.performInternalRiskCheck()
- 执行内部风控查询
- 复用现有的`buildRiskCreditApplyRequest`和风控调用逻辑
- 根据风控配置决定是否启用风控检查

## 复用的现有方法

1. **userRiskRecordRepository.queryThirtyDayRiskRejectRecord()** - 30天内风控拒绝检验
2. **orderRepository.queryThirtyDayCreditFailRecordLX()** - 30天内授信失败检验
3. **LvxinConvert.toBankChannelPub()** - 产品类型转资方渠道
4. **buildRiskCreditApplyRequest()** - 构建风控请求参数
5. **HttpUtil.post()** - HTTP请求调用

## 接口规范

### 请求示例
```json
{
    "idCardMd5": "身份证MD5值",
    "productType": "01"  // 01,02-长银, 03-华夏
}
```

### 响应示例
```json
{
    "code": "0000",
    "message": "成功",
    "data": {
        "result": 1,  // 1-通过, 2-拒绝
        "reason": "检验通过"
    }
}
```

## 错误处理

### 业务拒绝场景
1. 身份证MD5为空
2. 存在30天内同渠道风控拒绝记录
3. 存在30天内同渠道同资方授信失败记录
4. 内部风控未通过

### 系统异常场景
1. 数据库查询异常
2. 内部风控调用异常
3. 其他未预期异常

## 配置依赖

### 数据库查询
- 依赖`user_info`表的身份证号字段
- 依赖`user_risk_record`表的风控记录
- 依赖`order`表的授信失败记录

### 风控配置
- 依赖`riskConfig.getRiskEnable()`配置
- 依赖`riskConfig.getRiskUrl()`风控接口地址

## 测试建议

### 单元测试场景
1. 新用户（身份证MD5查不到）- 应该通过
2. 老用户无历史记录 - 应该通过
3. 30天内风控拒绝 - 应该拒绝
4. 30天内授信失败 - 应该拒绝
5. 内部风控拒绝 - 应该拒绝
6. 系统异常 - 应该返回异常状态

### 集成测试场景
1. 完整流程测试
2. 不同产品类型测试
3. 并发访问测试
4. 性能测试

## 部署注意事项

1. 确保数据库连接正常
2. 确保内部风控接口可访问
3. 监控接口调用量和响应时间
4. 关注错误日志和异常情况

## 后续优化建议

1. 考虑添加缓存机制提升查询性能
2. 考虑添加限流机制防止接口滥用
3. 考虑添加更详细的监控和告警
4. 根据业务需求调整检验规则的时间窗口
