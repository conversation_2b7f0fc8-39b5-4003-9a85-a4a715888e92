# 内部撞库接口设计方案

## 1. 需求分析

### 现状
- 现有撞库接口 `/api/partner/v1/access` 调用长银撞库
- 需要新增内部撞库接口，调用项目内部风控逻辑

### 目标
- 创建新的内部撞库接口
- 复用授信流程中的关键检验逻辑：
  - 30天内风控拒绝检验
  - 30天内授信失败检验
  - 内部风控查询
- 所有检验都通过才返回通过，任何一个不通过就返回失败

## 2. 技术方案

### 2.1 接口设计

**新接口路径**: `/api/partner/v1/internal-access`

**请求参数**: 复用现有 `UserCheckRequest`
```java
{
    "phoneMd5": "手机号MD5",
    "idCardMd5": "身份证MD5"
}
```

**响应参数**: 复用现有 `UserCheckResponse`
```java
{
    "result": 1,  // 1-通过, 2-拒绝
    "reason": "拒绝原因"
}
```

### 2.2 核心逻辑流程

```
1. 参数校验
   ↓
2. 30天内风控拒绝检验
   ↓ (通过)
3. 30天内授信失败检验  
   ↓ (通过)
4. 内部风控查询
   ↓ (通过)
5. 返回通过结果
```

### 2.3 实现方案

#### 方案A: 在现有Controller中新增方法
- 优点: 复用现有结构，改动最小
- 缺点: Controller职责增加

#### 方案B: 创建新的内部撞库Service
- 优点: 职责清晰，便于维护
- 缺点: 需要新增类文件

**推荐方案A**: 在现有Controller中新增方法，保持接口一致性

## 3. 详细实现

### 3.1 Controller层新增方法

```java
/**
 * 内部撞库
 */
@PostMapping({"/api/partner/v1/internal-access", "/api/partner/v2/internal-access"})
public LvxinResponse internalCheckUser(@RequestBody UserCheckRequest applyRequest) {
    UserCheckResponse response = lvxinService.internalUserCheck(applyRequest);
    return LvxinResponse.success(response);
}
```

### 3.2 Service层新增方法

```java
public UserCheckResponse internalUserCheck(UserCheckRequest request) {
    UserCheckResponse response = new UserCheckResponse();
    
    try {
        // 1. 参数校验
        validateInternalCheckParams(request);
        
        // 2. 根据MD5查找用户信息
        String idCard = findIdCardByMd5(request.getIdCardMd5());
        if (StringUtil.isBlank(idCard)) {
            response.setResult(2);
            response.setReason("用户信息不存在");
            return response;
        }
        
        // 3. 30天内风控拒绝检验
        List<UserRiskRecord> riskRecords = queryThirtyDayRiskRejectRecord(idCard, FlowChannel.LVXIN);
        if (!CollectionUtils.isEmpty(riskRecords)) {
            response.setResult(2);
            response.setReason("存在30天内风控拒绝记录");
            return response;
        }
        
        // 4. 30天内授信失败检验
        List<Order> failOrders = queryThirtyDayCreditFailRecord(idCard, FlowChannel.LVXIN, null);
        if (!CollectionUtils.isEmpty(failOrders)) {
            response.setResult(2);
            response.setReason("存在30天内授信失败记录");
            return response;
        }
        
        // 5. 内部风控查询
        boolean riskPass = performInternalRiskCheck(idCard, request);
        if (!riskPass) {
            response.setResult(2);
            response.setReason("内部风控未通过");
            return response;
        }
        
        // 6. 所有检验通过
        response.setResult(1);
        response.setReason("检验通过");
        
    } catch (Exception e) {
        logger.error("内部撞库检验异常", e);
        response.setResult(2);
        response.setReason("系统异常");
    }
    
    return response;
}
```

### 3.3 辅助方法

```java
/**
 * 执行内部风控检查
 */
private boolean performInternalRiskCheck(String idCard, UserCheckRequest request) {
    try {
        // 构建风控请求参数
        UserInfo userInfo = userInfoRepository.findByCertNo(idCard);
        if (userInfo == null) {
            return false;
        }
        
        // 创建临时风控记录用于查询
        UserRiskRecord tempRecord = createTempRiskRecord(userInfo, request);
        
        // 调用内部风控
        RiskDataRequest riskRequest = buildRiskCreditApplyRequest(tempRecord, userInfo);
        String response = HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(riskRequest));
        RiskDataResponse riskResult = JsonUtil.convertToObject(response, RiskDataResponse.class);
        
        // 判断风控结果
        return "PASS".equals(riskResult.getResult());
        
    } catch (Exception e) {
        logger.error("内部风控查询异常", e);
        return false;
    }
}

/**
 * 根据MD5查找身份证号
 */
private String findIdCardByMd5(String idCardMd5) {
    // 从用户信息表中根据MD5查找原始身份证号
    // 需要实现MD5反查逻辑或维护MD5映射表
    return userInfoRepository.findCertNoByMd5(idCardMd5);
}
```

## 4. 数据库支持

### 4.1 需要的查询方法

```java
// UserInfoRepository中新增
@Query("SELECT u.certNo FROM UserInfo u WHERE MD5(u.certNo) = :certNoMd5")
String findCertNoByMd5(@Param("certNoMd5") String certNoMd5);
```

## 5. 测试计划

### 5.1 单元测试
- 参数校验测试
- 30天内风控拒绝记录测试
- 30天内授信失败记录测试
- 内部风控查询测试

### 5.2 集成测试
- 完整流程测试
- 异常情况测试

## 6. 部署计划

1. 开发环境测试
2. 测试环境验证
3. 生产环境发布

## 7. 风险评估

### 7.1 技术风险
- MD5反查性能问题
- 内部风控接口稳定性

### 7.2 业务风险
- 撞库逻辑变更影响业务流程
- 新旧接口并存期间的一致性

## 8. 后续优化

1. 考虑缓存机制提升性能
2. 监控接口调用情况
3. 根据业务需求调整检验规则
