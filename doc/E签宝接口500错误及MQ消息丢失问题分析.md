# E签宝接口500错误及MQ消息丢失问题分析

## 问题现象
- E签宝接口调用返回500错误
- 短时间内频繁报错
- MQ自动扩容
- **flow模块查询capital模块的放款结果时数据丢失**

## 完整业务链路分析

### 1. 从签章到放款的完整流程

```
签章申请 → 签章结果查询 → 放款申请 → 放款结果查询
    ↓           ↓           ↓           ↓
SIGN_APPLY → SIGN_QUERY → LOAN_APPLY → LOAN_RECORD_QUERY
```

#### 1.1 签章申请阶段
- **队列**: `SIGN_APPLY`
- **监听器**: `SignApplyListener`
- **服务**: `AgreementService.signApply()`
- **调用**: E签宝接口 `signService.sign()`

#### 1.2 签章结果查询阶段（关键环节）
- **队列**: `SIGN_QUERY`
- **监听器**: `SignatureListener`
- **服务**: `AgreementService.querySign()`
- **延迟查询**: 通过`submitSignResultQueryDelay()`实现

#### 1.3 放款申请阶段
- **触发条件**: 签章完成后
- **队列**: `LOAN_APPLY`
- **监听器**: `LoanApplyListener`
- **服务**: `LoanService.loanApply()`

#### 1.4 放款结果查询阶段（问题焦点）
- **队列**: `LOAN_RECORD_QUERY`
- **监听器**: `LoanRecordResultListener`
- **服务**: `LoanService.bankLoanRecordResult()`
- **调用**: capital模块的`finLoanService.queryResult()`

## 根本原因分析

### 1. E签宝接口500错误的可能原因

#### 1.1 接口限流或过载
从代码分析可以看出，E签宝服务实现了限流机制：

```java
// ESignServiceImpl.java 第225-229行
private ResultMsg executeSignLogic(ESignDTO eSignDTO) throws EsignDemoException {
    // 再次检查限流
    if (!tryAcquireRateLimit()) {
        LOGGER.warn("E签宝API调用被限流阻断");
        throw new EsignDemoException("系统繁忙，请稍后再试");
    }
}
```

**可能原因**：
- E签宝第三方服务本身出现故障或过载
- 网络连接问题导致超时
- E签宝服务端限流策略触发

#### 1.2 业务逻辑异常
```java
// ESignServiceImpl.java 第319-324行
try {
    signedPdf = signContract(signAccount, eSignDTO, contractNo);
} catch (Exception e) {
    LOGGER.error("调用签章服务失败: {}", e.getMessage(), e);
    throw new EsignDemoException("签署合同失败，请稍后重试");
}
```

### 2. 消息丢失的关键链路分析

#### 2.1 签章环节的消息丢失风险

**签章申请失败场景**：
```java
// SignApplyListener.java
@RabbitListener(queues = RabbitConfig.Queues.SIGN_APPLY)
public void listenSignApply(Message message, Channel channel) {
    String signId = new String(message.getBody(), StandardCharsets.UTF_8);
    try {
        agreementService.signApply(signId); // 调用E签宝接口
    } catch (Exception e) {
        processException(signId, message, e, "申请协议签署异常", getMqService()::submitSignApplyDelay);
    } finally {
        ackMsg(signId, message, channel); // 无论成功失败都ACK
    }
}
```

**签章结果查询失败场景**：
```java
// SignatureListener.java
@RabbitListener(queues = RabbitConfig.Queues.SIGN_QUERY)
public void listenSignResult(Message message, Channel channel) {
    String signId = new String(message.getBody(), StandardCharsets.UTF_8);
    try {
        agreementService.querySign(signId); // 查询E签宝结果
    } catch (Exception e) {
        processException(signId, message, e, "查询签章结果异常", getMqService()::submitSignResultQueryDelay);
    } finally {
        ackMsg(signId, message, channel); // 无论成功失败都ACK
    }
}
```

#### 2.2 放款结果查询的消息丢失风险

**关键问题**：flow模块查询capital模块放款结果时的消息处理：

```java
// LoanRecordResultListener.java
@RabbitListener(queues = RabbitConfig.Queues.LOAN_RECORD_QUERY)
public void loanRecordResultListen(Message message, Channel channel) {
    String loanRecordId = new String(message.getBody(), StandardCharsets.UTF_8);
    try {
        loanService.bankLoanRecordResult(loanRecordId); // 查询capital放款结果
    } catch (Exception e) {
        processException(loanRecordId, message, e, "放款查询core异常", getMqService()::submitLoanRecordResultQueryDelay);
    } finally {
        ackMsg(loanRecordId, message, channel); // 无论成功失败都ACK
    }
}
```

#### 2.3 消息确认机制的根本问题

**核心缺陷**：在`AbstractListener`的异常处理逻辑中：

```java
// AbstractListener.java 第66-72行
protected void ackMsg(String body, Message message, Channel channel) {
    try {
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    } catch (IOException e) {
        logger.error("[{}][{}]消息确认异常", body, message.getMessageProperties().getConsumerQueue(), e);
    }
}
```

**问题分析**：
1. **无论业务处理成功还是失败，消息都会被确认（ACK）**
2. 在`finally`块中总是调用`ackMsg`，即使业务处理失败
3. 这导致失败的消息被确认后从队列中移除，无法重新处理

#### 2.2 重试机制的缺陷

```java
// AbstractListener.java 第49-64行
protected void processException(String body, Message message, Exception e, String desc, BiConsumer<String, Map<String, Object>> retry) {
    int retryTimes = retriedTimes(message);
    if (retryTimes == 1 && !(e instanceof CallBackException )) {
        // 首次预警
        mqWarningService.warn(body + "," + desc + "," + e.getMessage(), msg -> logger.error(msg, e));
    }
    if (isExceedMaxRetry(retryTimes)) {
        mqWarningService.warn(
            "[" + retryTimes + "][" + body + "]" + desc + "，超过最大重试次数，不再重试:" + e.getMessage(),
            msg -> logger.error(msg, e));
        return; // 关键问题：超过重试次数后直接返回，消息仍会被ACK
    }
    Map<String, Object> headers = new HashMap<>();
    headers.put(TRY_TIMES_KEY, retryTimes);
    retry.accept(body, headers); // 发送延迟重试消息
}
```

**问题**：
- 超过最大重试次数（3次）后，原消息仍会被ACK确认
- 重试是通过发送新的延迟消息实现，而不是拒绝原消息
- 如果延迟消息发送失败，消息就彻底丢失了

#### 2.3 MQ扩容时的消息丢失风险

当MQ自动扩容时：
1. **连接中断**：正在处理的消息可能因连接中断而丢失
2. **消费者重新分配**：消息可能在重新分配过程中丢失
3. **未确认消息处理**：如果消费者异常退出，未确认的消息可能丢失

### 3. 具体的消息丢失场景分析

#### 场景1：签章申请阶段的消息丢失
1. **触发**：用户授信通过，系统发送签章申请消息到`SIGN_APPLY`队列
2. **处理**：`SignApplyListener`接收消息，调用E签宝接口
3. **失败**：E签宝接口返回500错误，抛出异常
4. **重试**：`processException`发送延迟重试消息到`SIGN_APPLY_DELAY`队列
5. **确认**：**原消息在finally块中被ACK确认并从队列移除**
6. **丢失**：如果延迟消息发送失败或MQ扩容导致延迟消息丢失，签章申请彻底丢失

#### 场景2：签章结果查询阶段的消息丢失
1. **触发**：签章申请成功后，发送延迟查询消息到`SIGN_QUERY_DELAY`队列
2. **处理**：`SignatureListener`接收消息，查询E签宝结果
3. **失败**：E签宝接口返回500错误或查询失败
4. **重试**：发送新的延迟查询消息
5. **确认**：**原查询消息被ACK确认并移除**
6. **影响**：签章结果无法获取，后续放款流程无法触发

#### 场景3：放款结果查询阶段的消息丢失（核心问题）
1. **触发**：放款申请提交到capital后，发送查询消息到`LOAN_RECORD_QUERY`队列
2. **处理**：`LoanRecordResultListener`接收消息，调用`finLoanService.queryResult()`
3. **失败**：capital模块返回异常或网络超时
4. **重试**：发送延迟查询消息到`LOAN_RECORD_QUERY_DELAY`队列
5. **确认**：**原查询消息被ACK确认并移除**
6. **丢失**：如果延迟消息在MQ扩容时丢失，放款结果永远无法获取

#### 场景4：MQ扩容时的级联消息丢失
1. **起因**：E签宝接口频繁500错误，产生大量重试消息
2. **负载**：签章申请、签章查询、放款查询等多个队列消息积压
3. **扩容**：MQ负载增加，触发自动扩容
4. **中断**：扩容过程中连接中断，正在处理的消息丢失
5. **影响**：多个业务环节的延迟消息同时丢失，导致业务流程中断

## 解决方案

### 1. 立即修复方案

#### 1.1 修改消息确认逻辑
```java
// 修改AbstractListener的消息处理逻辑
@RabbitListener(queues = "xxx")
public void listen(Message message, Channel channel) {
    String body = new String(message.getBody(), StandardCharsets.UTF_8);
    try {
        // 业务处理
        processMessage(body);
        // 只有成功时才确认消息
        ackMsg(body, message, channel);
    } catch (Exception e) {
        // 处理异常，决定是重试还是拒绝消息
        handleException(body, message, channel, e);
    }
}

private void handleException(String body, Message message, Channel channel, Exception e) {
    int retryTimes = retriedTimes(message);
    if (isExceedMaxRetry(retryTimes)) {
        // 超过重试次数，拒绝消息并发送到死信队列
        nackMsg(body, message, channel, false);
    } else {
        // 拒绝消息，让其重新入队
        nackMsg(body, message, channel, true);
    }
}
```

#### 1.2 增加死信队列配置
```java
@Bean
public Queue signatureApplyDeadLetterQueue() {
    return QueueBuilder.durable("signature.apply.dlq").build();
}

@Bean
public Queue signatureApplyQueue() {
    return QueueBuilder.durable("signature.apply")
        .withArgument("x-dead-letter-exchange", "dlx.exchange")
        .withArgument("x-dead-letter-routing-key", "signature.apply.dlq")
        .withArgument("x-message-ttl", 300000) // 5分钟TTL
        .build();
}
```

### 2. 长期优化方案

#### 2.1 实现熔断机制
```java
@Component
public class ESignCircuitBreaker {
    private final CircuitBreaker circuitBreaker;
    
    public ResultMsg callESignWithCircuitBreaker(ESignDTO dto) {
        return circuitBreaker.executeSupplier(() -> {
            return callESignService(dto);
        });
    }
}
```

#### 2.2 增加消息持久化和监控
- 实现消息处理状态表，记录每条消息的处理状态
- 增加消息处理监控和告警
- 实现消息补偿机制

#### 2.3 优化重试策略
- 实现指数退避重试
- 区分不同类型的异常，采用不同的重试策略
- 对于500错误，应该重试；对于400错误，应该直接拒绝

## 紧急处理建议

### 1. 立即排查和止损
1. **检查死信队列**：查看各个业务队列的死信队列是否有未处理的消息
2. **检查延迟队列**：查看`SIGN_APPLY_DELAY`、`SIGN_QUERY_DELAY`、`LOAN_RECORD_QUERY_DELAY`等延迟队列状态
3. **暂停相关消费者**：临时停止签章和放款相关的消费者，防止更多消息丢失
4. **紧急修复消息确认逻辑**：确保失败的消息不会被错误确认

### 2. 数据恢复方案
1. **签章数据恢复**：
   - 查询`agreement_signature_record`表中状态为`PROCESSING`的记录
   - 手动重新发送签章申请或查询消息

2. **放款数据恢复**：
   - 查询`loan_record`表中状态为`PROCESSING`的记录
   - 手动调用capital模块查询放款结果
   - 对于已放款成功但flow模块未更新的记录，进行数据同步

3. **业务补偿处理**：
   - 通过业务日志分析丢失的消息范围
   - 制定批量补偿脚本，重新处理丢失的业务

## 预防措施

1. **完善的消息确认机制**：只有业务处理成功才确认消息
2. **死信队列配置**：处理失败的消息进入死信队列而不是丢失
3. **熔断和限流**：防止下游服务故障影响整个系统
4. **消息幂等性**：确保重复处理不会产生副作用
5. **完善的监控和告警**：及时发现和处理问题
