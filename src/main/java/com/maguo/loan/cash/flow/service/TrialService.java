package com.maguo.loan.cash.flow.service;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.repay.RepayTrailDto;
import com.jinghang.capital.api.dto.repay.TrailResultDto;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.convert.RepayConvert;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.CapitalConfig;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteePlan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entrance.common.constant.LvxinSysTimeMockService;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.FeeType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.remote.core.FinRepayService;
import com.maguo.loan.cash.flow.repository.BindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteePlanRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteeRecordRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.DateUtil;
import com.maguo.loan.cash.flow.util.ExceptionUtil;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 试算服务
 */
@Service
public class TrialService {
    private static final Logger logger = LoggerFactory.getLogger(TrialService.class);
    private FinRepayService finRepayService;
    private RepayPlanRepository repayPlanRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private BindCardRecordRepository bindCardRecordRepository;
    @Autowired
    private RepayExtraGuaranteePlanRepository feePlanRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private RepayExtraGuaranteeRecordRepository repayExtraGuaranteeRecordRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private CapitalConfigRepository capitalConfigRepository;
    @Autowired
    LvxinSysTimeMockService lvxinSysTimeMockService;
    public static final BigDecimal CONSULT_OVERDUE_RATE = new BigDecimal("0.0985");
    private static final BigDecimal PERCENT = BigDecimal.valueOf(100);


    public TrialResultVo repayTrial(String loanId, RepayPurpose repayPurpose, Integer period, String repayDate) {
        logger.info("试算服务接口请求参数,loanId:{},repayPurpose:{},period:{},repayDate{}", loanId, repayPurpose, period, repayDate);
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(loanId, period);
        if (Objects.isNull(repayDate)) {
            repayDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        Loan loan = loanRepository.findById(loanId).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
        Order order = orderRepository.findOrderById(loan.getOrderId());
        // 还款时间段校验
        repayDateCheck(loan, repayPurpose);
        BindCardRecord bindCardRecord = new BindCardRecord();
        RepayExtraGuaranteePlan feePlan = feePlanRepository.findByLoanIdAndPeriodAndPlanState(loanId, period, RepayState.NORMAL);
        if (Objects.nonNull(feePlan)) {
            // 借据还款卡id对应的绑卡记录
            bindCardRecord = bindCardRecordRepository.findById(loan.getRepayCardId())
                .orElseThrow(() -> new BizException(ResultCode.CARD_RECORD_NOT_EXIST));
            // 存在未还费用计划, 本地试算
            return feeTrial(bindCardRecord, feePlan);
        }
        //校验
        if (RepayPurpose.CURRENT == repayPurpose) {
            currentCheck(repayPlan);
        } else {
            clearCheck(loan, repayPurpose);
        }
        RestResult<TrailResultDto> restResult;
        try {

            // 调用fin-core试算
            RepayTrailDto trailDto = new RepayTrailDto();
            trailDto.setOuterLoanId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
            trailDto.setRepayPurpose(EnumConvert.INSTANCE.toCoreApi(repayPurpose));
            trailDto.setPeriod(period);
            trailDto.setLoanId(loan.getLoanNo());
            trailDto.setTransferDate(LocalDate.parse(repayDate.substring(0, 8), DateTimeFormatter.ofPattern("yyyyMMdd")));
            restResult = finRepayService.trial(trailDto);
            logger.info("请求fin-core试算结果 : {}", JsonUtil.toJsonString(restResult));
        } catch (Exception e) {
            logger.error("请求fin-core试算异常", e);
            throw new BizException(ResultCode.BIZ_ERROR);
        }
        if (!restResult.isSuccess()) {
            //不需要报警的异常映射
            ResultCode resultCode = ExceptionUtil.getResultCode(restResult.getMsg());
            if (Objects.nonNull(resultCode)) {
                throw new BizException(resultCode);
            }
            throw new BizException(restResult.getMsg(), ResultCode.REPAY_TRAIL_FAIL);
        }
        TrialResultVo trialResultVo = RepayConvert.INSTANCE.toVo(restResult.getData());
        trialResultVo.setPenalty(repayPlan.getPenaltyAmt());
        //延期入账罚息计算
        extensionPenalty(repayDate, repayPlan, trialResultVo);
        trialResultVo.setConsultFee(repayPlan.getConsultFee());
        //如果是权益类客户，取资方罚息
        if(order.getApproveRate() == RateLevel.RATE_24) {
            trialResultVo.setPenalty(restResult.getData().getOverdueFee());
        }
        if (RepayPurpose.CURRENT == repayPurpose) {
            trialResultVo.setGuaranteeFee(restResult.getData().getGuaranteeFee());
        } else {
            LocalDate repayDateMoc = LocalDate.parse(repayDate.substring(0, 8), DateTimeFormatter.ofPattern("yyyyMMdd"));
            LocalDateTime now = lvxinSysTimeMockService.isMockTime(repayDateMoc.atStartOfDay());
            YearMonth yearMonth = YearMonth.of(now.toLocalDate().getYear(), now.toLocalDate().getMonth());
            int daysInMonth = yearMonth.lengthOfMonth();
            long dateNum = 0;
            if (period == 1) {
                //5-20   5-15
                dateNum = DateUtil.dateDiff(loan.getLoanTime().toLocalDate(), now.toLocalDate());
            } else {
                RepayPlan plan = repayPlanRepository.findByLoanIdAndPeriod(loanId, period - 1);
                dateNum = DateUtil.dateDiff(plan.getPlanRepayDate(), now.toLocalDate());
            }
            //咨询费=月咨询费%当月天数*占用天数
            BigDecimal consultFee = planConsultFee(trialResultVo.getInterest(), trialResultVo.getPrincipal(), dateNum, daysInMonth);
            consultFee = consultFee.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : consultFee;
            trialResultVo.setConsultFee(AmountUtil.safeAmount(consultFee));
            //提前还款违约金
            //最后一期，不收取违约金
            if (period.equals(order.getApplyPeriods())) {
                trialResultVo.setBreachFee(BigDecimal.ZERO);
            } else {
                //剩余本金*3%-提前结清当期实还息费(利息+担保费+咨询服务费）
                BigDecimal bigDecimal = trialResultVo.getPrincipal().multiply(new BigDecimal("0.03"))
                    .subtract((trialResultVo.getInterest().add(consultFee)));
                BigDecimal breachFee = bigDecimal.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : bigDecimal;
                trialResultVo.setBreachFee(breachFee.setScale(2, RoundingMode.HALF_UP));
            }

            //到期日提前结清，只能先归还当期后再提前结清(注:到期日发起的提前结清只收取剩余未还本金)
            int previousPeriod = repayPlan.getPeriod() == 1 ? repayPlan.getPeriod() : repayPlan.getPeriod() - 1;
            if (previousPeriod > 0) {
                RepayPlan previousRepayPlan = repayPlanRepository.findByLoanIdAndPeriod(loanId, previousPeriod);
                if (previousRepayPlan.getPlanRepayDate().equals(now.toLocalDate())) {
                    trialResultVo.setInterest(BigDecimal.ZERO);
                    trialResultVo.setPenalty(BigDecimal.ZERO);
                    trialResultVo.setGuaranteeFee(BigDecimal.ZERO);
                    trialResultVo.setConsultFee(BigDecimal.ZERO);
                    trialResultVo.setBreachFee(BigDecimal.ZERO);
                }
            }
            //如果是lvxin权益客户；试算不包含罚息和违约金
            if(FlowChannel.LVXIN.equals(loan.getFlowChannel())&& IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())&&order.getApproveRate() == RateLevel.RATE_24){
                trialResultVo.setConsultFee(BigDecimal.ZERO);
                trialResultVo.setBreachFee(BigDecimal.ZERO);
            }
        }
//        if (order.getApproveRate() == RateLevel.RATE_24){
//            trialResultVo.setPenalty(BigDecimal.ZERO);
//            trialResultVo.setGuaranteeFee(BigDecimal.ZERO);
//            trialResultVo.setConsultFee(BigDecimal.ZERO);
//            trialResultVo.setBreachFee(BigDecimal.ZERO);
//        }
        trialResultVo.setAmount(AmountUtil.sum(trialResultVo.getPrincipal(), trialResultVo.getInterest(), trialResultVo.getPenalty(),
            trialResultVo.getGuaranteeFee(), trialResultVo.getConsultFee(), trialResultVo.getBreachFee()));
        logger.info("总金额为：{},本金为：{},利息为：{},罚息为：{},总融担费为：{}，咨询费为：{},违约金为：{}", trialResultVo.getAmount(), trialResultVo.getPrincipal(), trialResultVo.getInterest(), trialResultVo.getPenalty(), trialResultVo.getGuaranteeFee(), trialResultVo.getConsultFee(), trialResultVo.getBreachFee());
        trialResultVo.setBankName(bindCardRecord.getBankName());
        trialResultVo.setBankAccountCode(bindCardRecord.getBankCardNo());
        trialResultVo.setRemark(restResult.getMsg());
        logger.info("最终试算结果 : {}", JsonUtil.toJsonString(trialResultVo));
        return trialResultVo;
    }

    private void extensionPenalty(String repayDate, RepayPlan repayPlan, TrialResultVo trialResultVo) {
        if (!repayDate.equals(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))
            && repayPlan.getPenaltyAmt().compareTo(BigDecimal.ZERO) > 0) {
            // 实际还款时间与当前系统日期不一致 罚息重新就算
            BigDecimal totalAmtBase = AmountUtil.subtract(repayPlan.getPrincipalAmt(), repayPlan.getActPrincipalAmt());
            if (totalAmtBase.compareTo(BigDecimal.ZERO) <= 0) {
                totalAmtBase = BigDecimal.ZERO;
            }
            long overDay = DateUtil.dateDiff(repayPlan.getPlanRepayDate(), LocalDate.parse(repayDate.substring(0, 8), DateTimeFormatter.ofPattern("yyyyMMdd")));
            trialResultVo.setPenalty(totalAmtBase.multiply(new BigDecimal(overDay))
                .multiply(CONSULT_OVERDUE_RATE.divide(PERCENT)).setScale(2, RoundingMode.HALF_UP));
        }
    }

    //计算咨询费
    public BigDecimal planConsultFee(BigDecimal interestAmt, BigDecimal remainingPrincipalAmt, long num, int daysInMonth) {
        // 剩余本金 * 占用天数 0.12 /360 四舍五入 两位小数
        return remainingPrincipalAmt.multiply(new BigDecimal(num)).multiply(new BigDecimal("0.12"))
            .divide(new BigDecimal("360"), 2, RoundingMode.HALF_UP);
    }

    private TrialResultVo feeTrial(BindCardRecord bindCardRecord, RepayExtraGuaranteePlan feePlan) {
        // 校验是否存在进行中的第二部分扣款
        boolean existsRecord = repayExtraGuaranteeRecordRepository.existsByRepayPlanIdAndRepayStateIn(feePlan.getId(),
            List.of(ProcessState.INIT, ProcessState.PROCESSING, ProcessState.SUCCEED));
        if (existsRecord) {
            throw new BizException(ResultCode.REPAY_CHECK_ERROR);
        }

        TrialResultVo trialResultVo = new TrialResultVo();
        trialResultVo.setPrincipal(BigDecimal.ZERO);
        trialResultVo.setInterest(BigDecimal.ZERO);
        trialResultVo.setPenalty(BigDecimal.ZERO);
        trialResultVo.setBreachFee(BigDecimal.ZERO);

        //对客还款记录
        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findById(feePlan.getRepayRecordId()).orElseThrow();

        if (FeeType.GUARANTEE.equals(feePlan.getFeeType())) {
            trialResultVo.setGuaranteeFee(feePlan.getLeftAmount());
        }
        if (FeeType.CONSULT.equals(feePlan.getFeeType())) {
            trialResultVo.setConsultFee(customRepayRecord.getConsultFee());
            trialResultVo.setPenalty(AmountUtil.subtract(feePlan.getLeftAmount(), trialResultVo.getConsultFee()));
        }
        if (FeeType.GUARANTEE_CONSULT.equals(feePlan.getFeeType())) {
            trialResultVo.setGuaranteeFee(customRepayRecord.getGuaranteeAmt());
            trialResultVo.setConsultFee(customRepayRecord.getConsultFee());
            trialResultVo.setPenalty(AmountUtil.subtract(feePlan.getLeftAmount(), trialResultVo.getGuaranteeFee(), trialResultVo.getConsultFee()));
        }

        trialResultVo.setAmount(AmountUtil.sum(trialResultVo.getPrincipal(), trialResultVo.getInterest(), trialResultVo.getPenalty(),
            trialResultVo.getGuaranteeFee(), trialResultVo.getConsultFee()));

        trialResultVo.setBankName(bindCardRecord.getBankName());
        trialResultVo.setBankAccountCode(bindCardRecord.getBankCardNo());
        trialResultVo.setRepayPurpose(feePlan.getRepayPurpose());
        return trialResultVo;
    }

    private void currentCheck(RepayPlan repayPlan) {
        if (RepayState.REPAID == repayPlan.getCustRepayState()) {
            throw new BizException(ResultCode.CURRENT_PERIOD_REPAID);
        }

        // 获取最新一期还款
        RepayPlan minRepayPlan = repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(repayPlan.getLoanId(), RepayState.NORMAL).get(0);
        if (!Objects.equals(minRepayPlan.getPeriod(), repayPlan.getPeriod())) {
            throw new BizException("请先还第" + minRepayPlan.getPeriod() + "期", ResultCode.REPAY_PERIOD_ERROR);
        }

//        if (LocalDate.now().isBefore(repayPlan.getPlanRepayDate())) {
//            throw new BizException(ResultCode.REPAY_NOT_SUPPORTED_CURRENT);
//        }
    }


    private void clearCheck(Loan loan, RepayPurpose repayPurpose) {
        //获取最新一期还款
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loan.getId(), RepayState.NORMAL).get(0);

        // 逾期不允许结清
        if (repayPlan.getPlanRepayDate().isBefore(LocalDate.now())) {
            throw new BizException(ResultCode.REPAY_CLEAR_NOT_SUPPORTED_OVERDUE);
        }

        // 还款日当天不允许结清
      /*  if (repayPlan.getPlanRepayDate().isEqual(LocalDate.now())) {
            throw new BizException(ResultCode.REPAY_CLEAR_NOT_SUPPORTED_REPAY_DATE);
        }
*/
    }

    /**
     * 还款时间段校验
     *
     * @param loan
     */
    public void repayDateCheck(Loan loan, RepayPurpose repayPurpose) {
        if (loan.getLoanTime().toLocalDate().isEqual(LocalDate.now())) {
            throw new BizException(ResultCode.REPAY_NOT_SUPPORTED_LOAN_DATE);
        }
        // 校验还款时间段
        checkRepayTime(loan.getBankChannel(), repayPurpose);
    }


    /**
     * 还款时间段校验
     */
    private void checkRepayTime(BankChannel bankChannel, RepayPurpose repayPurpose) {
        CapitalConfig capitalConfig = capitalConfigRepository.findByBankChannel(bankChannel)
            .orElseThrow(() -> new BizException(ResultCode.CAPITAL_CONFIG_FAIL));
        if (capitalConfig.getRepayTimeStatus() == AbleStatus.DISABLE) {
            return;
        }
        LocalTime now = LocalTime.now();

        LocalTime repayStartTime = DateUtil.parse(capitalConfig.getRepayStartTime());
        LocalTime repayEndTime = DateUtil.parse(capitalConfig.getRepayEndTime());
        if (repayStartTime.isAfter(now) || repayEndTime.isBefore(now)) {
            logger.error("当前时间不能还款:{}", capitalConfig.getRepayStartTime() + "|" + capitalConfig.getRepayEndTime());
            throw new BizException(ResultCode.REPAY_TIME_CHECK_FAIL);
        }

    }

    @Autowired
    public void setFinRepayService(FinRepayService finRepayService) {
        this.finRepayService = finRepayService;
    }

    @Autowired
    public void setRepayPlanRepository(RepayPlanRepository repayPlanRepository) {
        this.repayPlanRepository = repayPlanRepository;
    }
}
