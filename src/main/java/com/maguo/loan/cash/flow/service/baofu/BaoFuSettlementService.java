package com.maguo.loan.cash.flow.service.baofu;

import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BaoFuSettlementStateCons;
import com.maguo.loan.cash.flow.config.BaoFuSettlementMerchantConfigManage;
import com.maguo.loan.cash.flow.entity.BaoFuWithdrawNotifyData;
import com.maguo.loan.cash.flow.entity.BfCashWithdrawalRecord;
import com.maguo.loan.cash.flow.entity.BfChargeRecord;
import com.maguo.loan.cash.flow.entity.BfSettlementNotify;
import com.maguo.loan.cash.flow.entity.BfTransferRecord;
import com.maguo.loan.cash.flow.enums.BaoFuSettlementStep;
import com.maguo.loan.cash.flow.repository.BfCashWithdrawalRecordRepository;
import com.maguo.loan.cash.flow.repository.BfChargeRecordRepository;
import com.maguo.loan.cash.flow.repository.BfSettlementNotifyRecordRepository;
import com.maguo.loan.cash.flow.repository.BfTransferRecordRepository;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.util.DateTimeUtils;
import com.rabbitmq.tools.json.JSONUtil;
import com.zsjz.third.part.baofoo.settlement.BaoFuAccountRechargeQueryService;
import com.zsjz.third.part.baofoo.settlement.BaoFuAccountRechargeService;
import com.zsjz.third.part.baofoo.settlement.BaoFuSettlementErrorCode;
import com.zsjz.third.part.baofoo.settlement.BaoFuSettlementException;
import com.zsjz.third.part.baofoo.settlement.BaoFuSettlementMerchantConfig;
import com.zsjz.third.part.baofoo.settlement.BaoFuTransferQueryService;
import com.zsjz.third.part.baofoo.settlement.BaoFuTransferService;
import com.zsjz.third.part.baofoo.settlement.BaoFuWithdrawalQueryService;
import com.zsjz.third.part.baofoo.settlement.BaoFuWithdrawalService;
import com.zsjz.third.part.baofoo.settlement.Entity.ContentBodyEntity;
import com.zsjz.third.part.baofoo.settlement.Entity.RsultBodyEntity;
import com.zsjz.third.part.baofoo.settlement.Entity.RsultTransferEntity;
import com.zsjz.third.part.baofoo.settlement.Entity.TransAccountEntity;
import com.zsjz.third.part.baofoo.settlement.Entity.ValuesAcctInfoEntity;
import com.zsjz.third.part.baofoo.settlement.Entity.ValuesActionEntity;
import com.zsjz.third.part.baofoo.vo.BaoFuSettlementInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Random;

import static com.maguo.loan.cash.flow.enums.BaoFuSettlementStep.CASH_WITHDRAWAL;
import static com.maguo.loan.cash.flow.enums.BaoFuSettlementStep.NOTIFY;
import static com.maguo.loan.cash.flow.enums.BaoFuSettlementStep.RECHARGE;
import static com.maguo.loan.cash.flow.enums.BaoFuSettlementStep.TRANSFER;

/**
 * 宝付渠道结算款来账通知 ——》通过 账户加值(分帐加值)给蚂蚁商户加值——》蚂蚁二级商户转账给湖消二级商户——》湖消提现——》企业微信通知
 */
@Slf4j
@Service
public class BaoFuSettlementService {
    @Autowired
    private BfCashWithdrawalRecordRepository bfCashWithdrawalRecordRepository;
    @Autowired
    private BfTransferRecordRepository bfTransferRecordRepository;
    @Autowired
    private BfChargeRecordRepository bfChargeRecordRepository;
    @Autowired
    private BfSettlementNotifyRecordRepository bfSettlementNotifyRecordRepository;
    @Autowired
    private BaoFuSettlementMerchantConfigManage baoFuSettlementMerchantConfigManage;
    @Autowired
    private BaoFuAccountRechargeService baoFuAccountRechargeService;
    @Autowired
    private BaoFuAccountRechargeQueryService baoFuAccountRechargeQueryService;
    @Autowired
    private BaoFuTransferService baoFuTransferService;
    @Autowired
    private BaoFuTransferQueryService baoFuTransferQueryService;
    @Autowired
    private BaoFuWithdrawalService baoFuWithdrawalService;
    @Autowired
    private BaoFuWithdrawalQueryService baoFuWithdrawalQueryService;
    @Autowired
    private WarningService warningService;

    @Autowired
    @Qualifier("settlementTaskExecutor")
    private ThreadPoolTaskExecutor settlementTaskExecutor;

    public void process(BaoFuSettlementInfo baoFuSettlementInfo) {
        settlementTaskExecutor.execute(() -> {
                processSettlement(baoFuSettlementInfo);
        });
    }

    /**
     * 实际的结算处理逻辑
     */
    protected void processSettlement(BaoFuSettlementInfo baoFuSettlementInfo) {
            try {
                BfSettlementNotify notify = bfSettlementNotifyRecordRepository.findByServerTransId(baoFuSettlementInfo.getServerTransId());
                if (notify == null) {
                    notify = saveSettlementNotify(baoFuSettlementInfo);
                }
                int attemptCount = 0;
                BfSettlementNotify previousState = getCurrentState(notify);
                while (!isTerminalState(notify) && attemptCount < 1) {
                    // 执行流程
                    continueProcess(notify, baoFuSettlementMerchantConfigManage.getActiveMerchantConfig());

                    // 重新加载最新状态
                    notify = bfSettlementNotifyRecordRepository.findByServerTransId(notify.getServerTransId());

                    // 检查是否推进
                    if (!isProgressMade(notify, previousState)) {
                        attemptCount++;
                    } else {
                        // 有进展则重置计数器
                        attemptCount = 0;
                    }
                    // 保存当前状态用于下次比较
                    previousState = getCurrentState(notify);
                }
            } catch (Exception e) {
                log.error("serverTransId={}，结算流程处理异常",baoFuSettlementInfo.getServerTransId(), e);
            }
    }

    /**
     * 定时任务补偿用，此时已确定数据存在，只需传入这个业务字段
     * @param serverTransId
     */
    public void processSettlement(String serverTransId) {
        BaoFuSettlementInfo baoFuSettlementInfo = new BaoFuSettlementInfo();
        baoFuSettlementInfo.setServerTransId(serverTransId);
        process(baoFuSettlementInfo);
    }

    // 辅助方法：判断是否为终态
    private boolean isTerminalState(BfSettlementNotify notify) {
        return notify.isFinished()  ||  notify.getStep() == CASH_WITHDRAWAL.getStep();
    }

    // 辅助方法：检查是否有进展
    private boolean isProgressMade(BfSettlementNotify current, BfSettlementNotify previous) {
        return !Objects.equals(current.getStep(), previous.getStep()) ||  current.getFinishStatus() != previous.getFinishStatus();
    }

    // 辅助方法：获取当前状态快照
    private BfSettlementNotify getCurrentState(BfSettlementNotify notify) {
        // 创建状态快照对象（仅包含关键状态字段）
        BfSettlementNotify snapshot = new BfSettlementNotify();
        snapshot.setStep(notify.getStep());
        snapshot.setFinishStatus(notify.getFinishStatus());
        return snapshot;
    }

    public void continueProcess(BfSettlementNotify notify, BaoFuSettlementMerchantConfig config) {
        // 使用安全的 fromStep 方法
        BaoFuSettlementStep step = BaoFuSettlementStep.fromStep(notify.getStep());
        switch (step) {
            case NOTIFY:
                step1(notify, config);
                break;
            case RECHARGE:
                step2(notify, config);
                break;
            case TRANSFER:
                step3(notify, config);
                break;
            case CASH_WITHDRAWAL:
                log.info("流程已完成: {}", notify.getServerTransId());
                break;
            default:
                log.warn("未知的流程步骤: {}", notify.getServerTransId());
        }
    }

    /**
     * 第一步：给二级商户账号（蚂蚁）加值
     *
     * @param bfSettlementNotify
     * @param baoFuSettlementMerchantConfig
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW) // 开启新事务
    public void step1(BfSettlementNotify bfSettlementNotify, BaoFuSettlementMerchantConfig baoFuSettlementMerchantConfig) {
        BfChargeRecord chargeRecord = bfChargeRecordRepository.findFirstByServerTransIdOrderByStateDesc(bfSettlementNotify.getServerTransId());
        if (bfSettlementNotify.getStep() == NOTIFY.getStep()) {
            log.info("serverTransId【{}】:执行二级商户加值业务",bfSettlementNotify.getServerTransId());
            // 没有执行过和执行失败的才被允许调用第三方接口
            if(null == chargeRecord || BaoFuSettlementStateCons.FAILED ==  chargeRecord.getState()) {
               // 组装请求报文
               List<ValuesAcctInfoEntity> listvaie = new ArrayList<>();
               ValuesAcctInfoEntity vaie1 = new ValuesAcctInfoEntity();
               vaie1.setContractNo(baoFuSettlementMerchantConfig.getMayiMemberId());
               vaie1.setTransAmount(String.valueOf(bfSettlementNotify.getAmount()));
               listvaie.add(vaie1);

               ValuesActionEntity vsae = new ValuesActionEntity();
               vsae.setVersion("1.0.0");
               vsae.setTransSerialNo(generateTransSerialNo(baoFuSettlementMerchantConfig.getMerchantId()));
               vsae.setReqTime(DateTimeUtils.format_yyyyMMddHHmmss(LocalDateTime.now()));
               vsae.setPlatformNo(baoFuSettlementMerchantConfig.getMerchantId());
               vsae.setAccountType("BALANCE");
               vsae.setTransOrderNo(bfSettlementNotify.getServerTransId());
               vsae.setOrderNo(bfSettlementNotify.getOrderId());
               vsae.setAcctInfo(listvaie);
               vsae.dealAmountAct();//统计总金额。
               ContentBodyEntity<RsultBodyEntity> rsultBody = baoFuAccountRechargeService.execute(vsae, baoFuSettlementMerchantConfig);
               RsultBodyEntity bodyEntity = rsultBody.getBody();
                if(chargeRecord == null) {
                    saveChargeRecord(bfSettlementNotify.getServerTransId(), vsae, bodyEntity);
                }else {
                    chargeRecord.setTransSerialNo(vsae.getTransSerialNo());
                    updateChargeRecord(bodyEntity,chargeRecord);
                }
                // 更新步骤并进入下一步
               if (bodyEntity.getState() == BaoFuSettlementStateCons.SUCCESS) {
                    bfSettlementNotify.setStep(RECHARGE.getStep());
                    bfSettlementNotify.setUpdatedTime(LocalDateTime.now());
                    bfSettlementNotifyRecordRepository.save(bfSettlementNotify);
                  }
             }else if(BaoFuSettlementStateCons.PROCESSING ==  chargeRecord.getState()){
                ValuesActionEntity vsae = new ValuesActionEntity();
                vsae.setVersion("1.0.0");
                vsae.setTransSerialNo(chargeRecord.getTransSerialNo());
                vsae.setReqTime(DateTimeUtils.format_yyyyMMddHHmmss(LocalDateTime.now()));
                vsae.setPlatformNo(baoFuSettlementMerchantConfig.getMerchantId());
                vsae.setTransOrderNo(chargeRecord.getServerTransId());
                vsae.setOrderType("01");//01:加值；02:减值；03：在途户结算余额户
                ContentBodyEntity<RsultBodyEntity> rsultBody = baoFuAccountRechargeQueryService.execute(vsae, baoFuSettlementMerchantConfig);
                RsultBodyEntity bodyEntity = rsultBody.getBody();
                //更新充值记录
                updateChargeRecord(bodyEntity,chargeRecord);
                // 更新步骤并进入下一步
                if (bodyEntity.getState() == BaoFuSettlementStateCons.SUCCESS) {
                    bfSettlementNotify.setStep(RECHARGE.getStep());
                    bfSettlementNotify.setUpdatedTime(LocalDateTime.now());
                    bfSettlementNotifyRecordRepository.save(bfSettlementNotify);
                }
            }
        }else {
            log.warn("serverTransId【{}】:跳过已执行的二级商户加值",bfSettlementNotify.getServerTransId());
        }
    }

    /**
     * 第二步：蚂蚁充值成功后，给湖消转账
     * @param bfSettlementNotify
     * @param baoFuSettlementMerchantConfig
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW) // 开启新事务
    public void step2(BfSettlementNotify bfSettlementNotify, BaoFuSettlementMerchantConfig baoFuSettlementMerchantConfig) {
        if (bfSettlementNotify.getStep() == RECHARGE.getStep()) {
            BfTransferRecord record = bfTransferRecordRepository.findFirstByServerTransIdOrderByStateDesc(bfSettlementNotify.getServerTransId());
            // 没有执行过和执行失败的才被允许调用转账接口
            if(null == record || BaoFuSettlementStateCons.FAILED ==  record.getState()) {
                TransAccountEntity transAccountEntity = new TransAccountEntity();
                transAccountEntity.setVersion("1.0.0");
                transAccountEntity.setPayerNo(baoFuSettlementMerchantConfig.getMayiMemberId());//付款方(二级子商户号)
                transAccountEntity.setPayeeNo(baoFuSettlementMerchantConfig.getHuxiaoMemberId());//收款方(二级子商户号)
                transAccountEntity.setTransSerialNo(generateTransSerialNo(baoFuSettlementMerchantConfig.getMerchantId()));
                transAccountEntity.setAccountType("BALANCE");
                transAccountEntity.setDealAmount(bfSettlementNotify.getAmount());//转账金额,单位：元
                ContentBodyEntity<RsultTransferEntity> transferEntity = baoFuTransferService.execute(transAccountEntity, baoFuSettlementMerchantConfig);
                RsultTransferEntity bodyEntity = transferEntity.getBody();
                if(record == null) {
                    saveTransferRecord(bfSettlementNotify.getServerTransId(), transAccountEntity, bodyEntity);
                }else {
                    record.setTransSerialNo(transAccountEntity.getTransSerialNo());
                    updateTransferRecord(bodyEntity,record);
                }
                // 更新步骤并进入下一步
                if (bodyEntity.getState() == BaoFuSettlementStateCons.SUCCESS) {
                    bfSettlementNotify.setStep(TRANSFER.getStep());
                    bfSettlementNotify.setUpdatedTime(LocalDateTime.now());
                    bfSettlementNotifyRecordRepository.save(bfSettlementNotify);
                }
            }else if(BaoFuSettlementStateCons.PROCESSING ==  record.getState()){
                // 调用转账查询接口
                TransAccountEntity tracce = new TransAccountEntity();
                tracce.setVersion("1.0.0");
                tracce.setTransSerialNo(record.getTransSerialNo());
                tracce.setTradeTime(DateTimeUtils.format_yyyy_MM_dd(record.getCreatedTime()));
                ContentBodyEntity<RsultTransferEntity> contentBodyEntity  = baoFuTransferQueryService.execute(tracce, baoFuSettlementMerchantConfig);
                RsultTransferEntity entityBody = contentBodyEntity.getBody();
                // 更新转账记录
                updateTransferRecord(entityBody,record);
                // 更新步骤并进入下一步
                if (entityBody.getState() == BaoFuSettlementStateCons.SUCCESS) {
                    bfSettlementNotify.setStep(TRANSFER.getStep());
                    bfSettlementNotify.setUpdatedTime(LocalDateTime.now());
                    bfSettlementNotify = bfSettlementNotifyRecordRepository.save(bfSettlementNotify);
                }
            }
        }else {
            log.warn("serverTransId【{}】:跳过已执行的给二级商户转账",bfSettlementNotify.getServerTransId());
        }

    }

    /**
     * 第三步：湖消提现
     * @param bfSettlementNotify
     * @param baoFuSettlementMerchantConfig
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW) // 开启新事务
    public void step3(BfSettlementNotify bfSettlementNotify, BaoFuSettlementMerchantConfig baoFuSettlementMerchantConfig) {
        if (bfSettlementNotify.getStep() == TRANSFER.getStep()) {
            BfCashWithdrawalRecord record = bfCashWithdrawalRecordRepository.findFirstByServerTransIdOrderByStateDesc(bfSettlementNotify.getServerTransId());
            // 没有执行过和执行失败的才被允许调用第三方接口
            if(null == record || BaoFuSettlementStateCons.FAILED ==  record.getState()) {

                TransAccountEntity transAccountEntity = new TransAccountEntity();
                transAccountEntity.setVersion("1.0.0");
                transAccountEntity.setContractNo(baoFuSettlementMerchantConfig.getHuxiaoMemberId());// 客户账户号(湖消)
                transAccountEntity.setTransSerialNo(generateTransSerialNo(baoFuSettlementMerchantConfig.getMerchantId()));
                transAccountEntity.setDealAmount(bfSettlementNotify.getAmount());//转账金额,单位：元
                transAccountEntity.setReturnUrl(baoFuSettlementMerchantConfig.getReturnUrl());//通知地址
                transAccountEntity.setFeeMemberId(baoFuSettlementMerchantConfig.getHuxiaoMemberId());

                ContentBodyEntity<RsultTransferEntity> bodyEntity = baoFuWithdrawalService.execute(transAccountEntity, baoFuSettlementMerchantConfig);
                RsultTransferEntity transferEntity = bodyEntity.getBody();
                // 保存提现记录
                saveWithdrawalRecord(bfSettlementNotify.getServerTransId(), transAccountEntity, transferEntity);
                // 更新步骤
                if (transferEntity.getState() == BaoFuSettlementStateCons.SUCCESS) {
                    bfSettlementNotify.setStep(CASH_WITHDRAWAL.getStep());
                    bfSettlementNotify.setUpdatedTime(LocalDateTime.now());
                    bfSettlementNotify.setFinishStatus(BaoFuSettlementStateCons.NOTIFY_FINISHED);
                    bfSettlementNotifyRecordRepository.save(bfSettlementNotify);

                    // 对比结算款通知的金额和实际商户提现的金额,发送企业微信
                    sendMsg(bfSettlementNotify.getServerTransId(),bfSettlementNotify.getAmount(),bodyEntity.getBody().getDealAmount());
                }
            }else if(BaoFuSettlementStateCons.PROCESSING ==  record.getState()){
                // 调用提现查询接口
                TransAccountEntity tracce = new TransAccountEntity();
                tracce.setVersion("1.0.0");
                tracce.setTransSerialNo(record.getTransSerialNo());
                tracce.setTradeTime(DateTimeUtils.format_yyyy_MM_dd(record.getCreatedTime()));
                ContentBodyEntity<RsultTransferEntity> contentBodyEntity  = baoFuWithdrawalQueryService.execute(tracce, baoFuSettlementMerchantConfig);
                RsultTransferEntity transferEntity = contentBodyEntity.getBody();
                // 更新提现记录
                updateCashWithdrawalRecord(transferEntity,record);
                // 更新步骤
                if (transferEntity.getState() == BaoFuSettlementStateCons.SUCCESS) {
                    bfSettlementNotify.setStep(CASH_WITHDRAWAL.getStep());
                    bfSettlementNotify.setUpdatedTime(LocalDateTime.now());
                    bfSettlementNotify.setFinishStatus(BaoFuSettlementStateCons.NOTIFY_FINISHED);
                    bfSettlementNotifyRecordRepository.save(bfSettlementNotify);

                    // 对比结算款通知的金额和实际商户提现的金额,发送企业微信
                    sendMsg(bfSettlementNotify.getServerTransId(),bfSettlementNotify.getAmount(),transferEntity.getDealAmount());
                }
            }
        }else {
            log.warn("serverTransId【{}】:跳过已执行的商户提现",bfSettlementNotify.getServerTransId());
        }
    }

    /**
     * 对比结算款通知的金额和实际商户提现的金额,发送企业微信
     * @param notifyAmount
     * @param cashWithdrawalAmount
     */
    private  void sendMsg(String serverTransId, BigDecimal notifyAmount,  BigDecimal cashWithdrawalAmount) {
        // 对比结算款通知的金额和实际商户提现的金额
        String currTime = DateTimeUtils.toChineseDateTime(LocalDateTime.now());
        StringBuilder sb = new StringBuilder();
        sb.append(currTime);
        sb.append(",【拍拍-蚂蚁湖消】项目");
        sb.append("serverTransId【");
        sb.append(serverTransId);
        sb.append("】");
        sb.append("，线下还款资金流转账金额");
        sb.append(cashWithdrawalAmount);
        sb.append("元,");
        if(notifyAmount.compareTo(cashWithdrawalAmount) != 0){
            sb.append("转账失败,金额不一致。(渠道结算款来账通知金额为");
            sb.append(notifyAmount);
            sb.append("元)");
        }else {
            sb.append("转账成功，金额一致");
        }
        warningService.warn(sb.toString());
    }

    public boolean baoFuWithdrawalNotify(BaoFuWithdrawNotifyData notifyData){
        BfCashWithdrawalRecord record = bfCashWithdrawalRecordRepository.findByTransSerialNo(notifyData.getTransSerialNo());
        if(ObjectUtils.isEmpty(record)){
            log.error("宝付二级商户提现记录为null，notifyData={}",  JsonUtil.toJsonString(notifyData));
            throw new BaoFuSettlementException(BaoFuSettlementErrorCode.DATA_ERROR);
        }
        int state = Integer.parseInt(notifyData.getState());
        record.setUpdatedTime(LocalDateTime.now());
        record.setState( state);
        bfCashWithdrawalRecordRepository.save(record);
        // 提现成功，更新数据
        BfSettlementNotify bfSettlementNotify = bfSettlementNotifyRecordRepository.findByServerTransId(record.getServerTransId());
        bfSettlementNotify.setUpdatedTime(LocalDateTime.now());

        if(BaoFuSettlementStateCons.SUCCESS  ==  state ){
            bfSettlementNotify.setStep(CASH_WITHDRAWAL.getStep());
            bfSettlementNotify.setFinishStatus(BaoFuSettlementStateCons.NOTIFY_FINISHED);
            sendMsg(bfSettlementNotify.getServerTransId(),bfSettlementNotify.getAmount(),notifyData.getTransMoney());
        }else if(BaoFuSettlementStateCons.ROLL_BACK ==  record.getState()){
            // 3提现退回，终端不再继续
            bfSettlementNotify.setFinishStatus(BaoFuSettlementStateCons.NOTIFY_BREAK);
            bfSettlementNotifyRecordRepository.save(bfSettlementNotify);
        }else if(BaoFuSettlementStateCons.PROCESSING ==  record.getState()){
            // 业务处理中，需要继续通知
            return true;
        }
        return false;
    }


    /**
     * 保存蚂蚁充值记录
     * @param serverTransId
     * @param valuesAction
     * @param resultBodyEntity
     */
    private void saveChargeRecord(String serverTransId, ValuesActionEntity valuesAction,RsultBodyEntity resultBodyEntity) {
        BfChargeRecord chargeRecord  =new BfChargeRecord();
        chargeRecord.setAccountType(valuesAction.getAccountType());
        chargeRecord.setContractNo(valuesAction.getAcctInfo().get(0).getContractNo());
        chargeRecord.setOrderNo(valuesAction.getOrderNo());
        chargeRecord.setServerTransId(serverTransId);
        chargeRecord.setRetCode(resultBodyEntity.getRetCode());
        chargeRecord.setState(resultBodyEntity.getState());
        chargeRecord.setTransAmount(new BigDecimal(valuesAction.getAcctInfo().get(0).getTransAmount()));
        chargeRecord.setTransSerialNo(valuesAction.getTransSerialNo());
        chargeRecord.setErrorMsg(resultBodyEntity.getErrorMsg());
        chargeRecord.setErrorCode(resultBodyEntity.getErrorCode());
        bfChargeRecordRepository.save(chargeRecord);
    }

    /**
     * 更新蚂蚁充值记录
     * @param bodyEntity
     * @param chargeRecord
     */
    private void updateChargeRecord(RsultBodyEntity bodyEntity,BfChargeRecord chargeRecord){
        chargeRecord.setRetCode(bodyEntity.getRetCode());
        chargeRecord.setErrorCode(bodyEntity.getErrorCode());
        chargeRecord.setErrorMsg(bodyEntity.getErrorMsg());
        chargeRecord.setState(bodyEntity.getState());
        chargeRecord.setUpdatedTime(LocalDateTime.now());
        bfChargeRecordRepository.save(chargeRecord);
    }


    /**
     * 保存二级商户转账记录
     * @param serverTransId
     * @param transAccountEntity
     * @param rsultTransferEntity
     */
    private void saveTransferRecord(String serverTransId,TransAccountEntity transAccountEntity, RsultTransferEntity rsultTransferEntity) {
        BfTransferRecord bfTransferRecord = new BfTransferRecord();
        bfTransferRecord.setAccountType(transAccountEntity.getAccountType());
        bfTransferRecord.setServerTransId(serverTransId);
        bfTransferRecord.setDealAmount(transAccountEntity.getDealAmount());
        bfTransferRecord.setBusinessNo(rsultTransferEntity.getBusinessNo());
        bfTransferRecord.setFeeAmount(rsultTransferEntity.getFeeAmount());
        bfTransferRecord.setPayeeNo(rsultTransferEntity.getPayeeNo());
        bfTransferRecord.setPayerNo(rsultTransferEntity.getPayerNo());
        bfTransferRecord.setErrorMsg(rsultTransferEntity.getErrorMsg());
        bfTransferRecord.setErrorCode(rsultTransferEntity.getErrorCode());
        bfTransferRecord.setRetCode(rsultTransferEntity.getRetCode());
        bfTransferRecord.setState(rsultTransferEntity.getState());
        bfTransferRecord.setTransSerialNo(transAccountEntity.getTransSerialNo());
        bfTransferRecordRepository.save(bfTransferRecord);
    }

    private void updateTransferRecord(RsultTransferEntity transferEntity,BfTransferRecord transferRecord){
        transferRecord.setRetCode(transferEntity.getRetCode());
        transferRecord.setErrorCode(transferEntity.getErrorCode());
        transferRecord.setErrorMsg(transferEntity.getErrorMsg());
        transferRecord.setState(transferEntity.getState());
        transferRecord.setUpdatedTime(LocalDateTime.now());
        bfTransferRecordRepository.save(transferRecord);
    }

    private void updateCashWithdrawalRecord(RsultTransferEntity transferEntity,BfCashWithdrawalRecord record){
        record.setRetCode(transferEntity.getRetCode());
        record.setErrorCode(transferEntity.getErrorCode());
        record.setErrorMsg(transferEntity.getErrorMsg());
        record.setState(transferEntity.getState());
        record.setUpdatedTime(LocalDateTime.now());
        bfCashWithdrawalRecordRepository.save(record);
    }

    /**
     * 保存二级商户提现记录
     * @param serverTransId
     * @param transAccountEntity
     * @param rsultTransferEntity
     */
    private void saveWithdrawalRecord(String serverTransId,TransAccountEntity transAccountEntity, RsultTransferEntity rsultTransferEntity) {
        BfCashWithdrawalRecord bfCashWithdrawalRecord = new BfCashWithdrawalRecord();
        bfCashWithdrawalRecord.setServerTransId(serverTransId);
        bfCashWithdrawalRecord.setContractNo(transAccountEntity.getContractNo());
        bfCashWithdrawalRecord.setDealAmount(transAccountEntity.getDealAmount());
        bfCashWithdrawalRecord.setTransSerialNo(transAccountEntity.getTransSerialNo());
        bfCashWithdrawalRecord.setErrorMsg(rsultTransferEntity.getErrorMsg());
        bfCashWithdrawalRecord.setErrorCode(rsultTransferEntity.getErrorCode());
        bfCashWithdrawalRecord.setRetCode(rsultTransferEntity.getRetCode());
        bfCashWithdrawalRecord.setState(rsultTransferEntity.getState());
        bfCashWithdrawalRecordRepository.save(bfCashWithdrawalRecord);
    }


    /**
     * 保存宝付结算款通知
     * @param baoFuSettlementInfo
     * @return
     */
    private BfSettlementNotify saveSettlementNotify(BaoFuSettlementInfo baoFuSettlementInfo) {
        BfSettlementNotify bfSettlementNotify = new BfSettlementNotify();
        bfSettlementNotify.setAmount(baoFuSettlementInfo.getAmount());
        bfSettlementNotify.setOrderId(baoFuSettlementInfo.getOrderId());
        bfSettlementNotify.setBankType(baoFuSettlementInfo.getBankType());
        bfSettlementNotify.setStep(NOTIFY.getStep());
        bfSettlementNotify.setBankType(baoFuSettlementInfo.getBankType());
        bfSettlementNotify.setRecordTime(baoFuSettlementInfo.getRecordDateTime());
        bfSettlementNotify.setSeqNo(baoFuSettlementInfo.getSeqNo());
        bfSettlementNotify = bfSettlementNotifyRecordRepository.save(bfSettlementNotify);
        return bfSettlementNotify;
    }

    private String generateTransSerialNo(String merchantId) {
        String timestamp = DateTimeUtils.format_yyyyMMddHHmmss(LocalDateTime.now());
        String random = String.format("%06d", new Random().nextInt(1000000));
        return merchantId + timestamp + random;
    }
}
