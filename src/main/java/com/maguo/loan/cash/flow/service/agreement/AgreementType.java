package com.maguo.loan.cash.flow.service.agreement;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.WhetherState;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
public enum AgreementType {

    /***
     * 绿信-长银 5份协议
     */
    //授信（个人敏感信息授权书-修订）
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER(FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER, ".pdf", "00004", LoanStage.RISK,
        FlowChannel.LVXIN,BankChannel.CYBK, false, WhetherState.Y),

    //授信（综合授权书-担保）
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE(FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE, ".pdf", "00002", LoanStage.RISK,
        FlowChannel.LVXIN,BankChannel.CYBK, false, WhetherState.Y),

    //咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT(FileType.CONSULTING_SERVICE_CONTRACT, ".pdf", "00001", LoanStage.LOAN,
        FlowChannel.LVXIN,BankChannel.CYBK, false, WhetherState.Y),
    //承诺书.pdf
    LETTER_OF_COMMITMENT(FileType.LETTER_OF_COMMITMENT, ".pdf", "00007", LoanStage.LOAN,
        FlowChannel.LVXIN,BankChannel.CYBK, false, WhetherState.Y),
    //委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE(FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE, ".pdf", "00003", LoanStage.LOAN,
        FlowChannel.LVXIN,BankChannel.CYBK, false, WhetherState.Y),

    /***
     * 绿信-润楼 5份协议
     */
    //授信（个人敏感信息授权书-修订）
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER_LXRL(FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER, ".pdf", "00004", LoanStage.RISK,
        FlowChannel.LVXIN,BankChannel.RL_SUS, false, WhetherState.Y),

    //授信（综合授权书-担保）
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE_LXRL(FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE, ".pdf", "00002", LoanStage.RISK,
        FlowChannel.LVXIN,BankChannel.RL_SUS, false, WhetherState.Y),

    //咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT_LXRL(FileType.CONSULTING_SERVICE_CONTRACT, ".pdf", "00001", LoanStage.LOAN,
        FlowChannel.LVXIN,BankChannel.RL_SUS, false, WhetherState.Y),
    //承诺书.pdf
    LETTER_OF_COMMITMENT_LXRL(FileType.LETTER_OF_COMMITMENT, ".pdf", "00007", LoanStage.LOAN,
        FlowChannel.LVXIN,BankChannel.RL_SUS, false, WhetherState.Y),
    //委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE_LXRL(FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE, ".pdf", "00003", LoanStage.LOAN,
        FlowChannel.LVXIN,BankChannel.RL_SUS, false, WhetherState.Y),

    /***
     * 绿信-湖消 5份协议
     */
    //授信（个人敏感信息授权书-修订）
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER_LXHX(FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER, ".pdf", "00013", LoanStage.RISK,
        FlowChannel.LVXIN,BankChannel.HXBK, false, WhetherState.Y),
    //授信（综合授权书-担保）=> 综合授权书-担保（四合一）
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE_LXHX(FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE, ".pdf", "00011", LoanStage.RISK,
        FlowChannel.LVXIN,BankChannel.HXBK, false, WhetherState.Y),

    //咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT_LXHX(FileType.CONSULTING_SERVICE_CONTRACT, ".pdf", "00010", LoanStage.LOAN,
        FlowChannel.LVXIN,BankChannel.HXBK, false, WhetherState.Y),
    //承诺书.pdf
    LETTER_OF_COMMITMENT_LXHX(FileType.LETTER_OF_COMMITMENT, ".pdf", "00014", LoanStage.LOAN,
        FlowChannel.LVXIN,BankChannel.HXBK, false, WhetherState.Y),
    //委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE_LXHX(FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE, ".pdf", "00012", LoanStage.LOAN,
        FlowChannel.LVXIN,BankChannel.HXBK, false, WhetherState.Y),


    /***
     * 拍拍-长银 5份协议
     */
    //授信（个人敏感信息授权书-修订）
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER_PPCY(FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER, ".pdf", "00004", LoanStage.RISK,
        FlowChannel.PPCJDL,BankChannel.CYBK, false, WhetherState.Y),

    //授信（综合授权书-担保）
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE_PPCY(FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE, ".pdf", "00002", LoanStage.RISK,
        FlowChannel.PPCJDL,BankChannel.CYBK, false, WhetherState.Y),

    //咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT_PPCY(FileType.CONSULTING_SERVICE_CONTRACT, ".pdf", "00001", LoanStage.LOAN,
        FlowChannel.PPCJDL,BankChannel.CYBK, false, WhetherState.Y),
    //承诺书.pdf
    LETTER_OF_COMMITMENT_PPCY(FileType.LETTER_OF_COMMITMENT, ".pdf", "00007", LoanStage.LOAN,
        FlowChannel.PPCJDL,BankChannel.CYBK, false, WhetherState.Y),
    //委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE_PPCY(FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE, ".pdf", "00003", LoanStage.LOAN,
        FlowChannel.PPCJDL,BankChannel.CYBK, false, WhetherState.Y),

    /***
     * 拍拍-润楼 5份协议
     */
    //授信（个人敏感信息授权书-修订）
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER_PPRL(FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER, ".pdf", "00004", LoanStage.RISK,
        FlowChannel.PPCJDL,BankChannel.RL_SUS, false, WhetherState.Y),

    //授信（综合授权书-担保）
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE_PPRL(FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE, ".pdf", "00002", LoanStage.RISK,
        FlowChannel.PPCJDL,BankChannel.RL_SUS, false, WhetherState.Y),

    //咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT_PPRL(FileType.CONSULTING_SERVICE_CONTRACT, ".pdf", "00001", LoanStage.LOAN,
        FlowChannel.PPCJDL,BankChannel.RL_SUS, false, WhetherState.Y),
    //承诺书.pdf
    LETTER_OF_COMMITMENT_PPRL(FileType.LETTER_OF_COMMITMENT, ".pdf", "00007", LoanStage.LOAN,
        FlowChannel.PPCJDL,BankChannel.RL_SUS, false, WhetherState.Y),
    //委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE_PPRL(FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE, ".pdf", "00003", LoanStage.LOAN,
        FlowChannel.PPCJDL,BankChannel.RL_SUS, false, WhetherState.Y),

    /***
     * 拍拍-湖消 5份协议
     */
    //授信（个人敏感信息授权书-修订）
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER_PPHX(FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER, ".pdf", "00004", LoanStage.RISK,
        FlowChannel.PPCJDL,BankChannel.HXBK, false, WhetherState.Y),

    //授信（综合授权书-担保）
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE_PPHX(FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE, ".pdf", "00002", LoanStage.RISK,
        FlowChannel.PPCJDL,BankChannel.HXBK, false, WhetherState.Y),

    //咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT_PPHX(FileType.CONSULTING_SERVICE_CONTRACT, ".pdf", "00001", LoanStage.LOAN,
        FlowChannel.PPCJDL,BankChannel.HXBK, false, WhetherState.Y),
    //承诺书.pdf
    LETTER_OF_COMMITMENT_PPHX(FileType.LETTER_OF_COMMITMENT, ".pdf", "00007", LoanStage.LOAN,
        FlowChannel.PPCJDL,BankChannel.HXBK, false, WhetherState.Y),
    //委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE_PPHX(FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE, ".pdf", "00003", LoanStage.LOAN,
        FlowChannel.PPCJDL,BankChannel.HXBK, false, WhetherState.Y),



    /***
     * 同程-长银 5份协议
     */
    //授信（个人敏感信息授权书-修订）
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER_TCCY(FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER, ".pdf", "00004", LoanStage.RISK,
        FlowChannel.TONG_CHENG,BankChannel.CYBK, false, WhetherState.Y),

    //授信（综合授权书-担保）
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE_TCCY(FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE, ".pdf", "00002", LoanStage.RISK,
        FlowChannel.TONG_CHENG,BankChannel.CYBK, false, WhetherState.Y),

    //咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT_TCCY(FileType.CONSULTING_SERVICE_CONTRACT, ".pdf", "00001", LoanStage.LOAN,
        FlowChannel.TONG_CHENG,BankChannel.CYBK, false, WhetherState.Y),
    //承诺书.pdf
    LETTER_OF_COMMITMENT_TCCY(FileType.LETTER_OF_COMMITMENT, ".pdf", "00007", LoanStage.LOAN,
        FlowChannel.TONG_CHENG,BankChannel.CYBK, false, WhetherState.Y),
    //委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE_TCCY(FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE, ".pdf", "00003", LoanStage.LOAN,
        FlowChannel.TONG_CHENG,BankChannel.CYBK, false, WhetherState.Y),

    /***
     * 同程-润楼 5份协议
     */
    //授信（个人敏感信息授权书-修订）
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER_TCRL(FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER, ".pdf", "00004", LoanStage.RISK,
        FlowChannel.TONG_CHENG,BankChannel.RL_SUS, false, WhetherState.Y),

    //授信（综合授权书-担保）
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE_TCRL(FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE, ".pdf", "00002", LoanStage.RISK,
        FlowChannel.TONG_CHENG,BankChannel.RL_SUS, false, WhetherState.Y),

    //咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT_TCRL(FileType.CONSULTING_SERVICE_CONTRACT, ".pdf", "00001", LoanStage.LOAN,
        FlowChannel.TONG_CHENG,BankChannel.RL_SUS, false, WhetherState.Y),
    //承诺书.pdf
    LETTER_OF_COMMITMENT_TCRL(FileType.LETTER_OF_COMMITMENT, ".pdf", "00007", LoanStage.LOAN,
        FlowChannel.TONG_CHENG,BankChannel.RL_SUS, false, WhetherState.Y),
    //委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE_TCRL(FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE, ".pdf", "00003", LoanStage.LOAN,
        FlowChannel.TONG_CHENG,BankChannel.RL_SUS, false, WhetherState.Y),

    /***
     * 同程-湖消 5份协议
     */
    //授信（个人敏感信息授权书-修订）
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER_TCHX(FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER, ".pdf", "00004", LoanStage.RISK,
        FlowChannel.TONG_CHENG,BankChannel.HXBK, false, WhetherState.Y),

    //授信（综合授权书-担保）
    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE_TCHX(FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE, ".pdf", "00002", LoanStage.RISK,
        FlowChannel.TONG_CHENG,BankChannel.HXBK, false, WhetherState.Y),

    //咨询服务合同(超捷).pdf
    CONSULTING_SERVICE_CONTRACT_TCHX(FileType.CONSULTING_SERVICE_CONTRACT, ".pdf", "00001", LoanStage.LOAN,
        FlowChannel.TONG_CHENG,BankChannel.HXBK, false, WhetherState.Y),
    //承诺书.pdf
    LETTER_OF_COMMITMENT_TCHX(FileType.LETTER_OF_COMMITMENT, ".pdf", "00007", LoanStage.LOAN,
        FlowChannel.TONG_CHENG,BankChannel.HXBK, false, WhetherState.Y),
    //委托扣款授权书-担保vs借款人.pdf
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE_TCHX(FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE, ".pdf", "00003", LoanStage.LOAN,
        FlowChannel.TONG_CHENG,BankChannel.HXBK, false, WhetherState.Y),
    ;


    private final FileType fileType;
    private final String extension;

    private final String templateNo;

    private final LoanStage loanStage;

    /**
     * 资方渠道
     */
    private final FlowChannel flowChannel;
    /**
     * 资方渠道
     */
    private final BankChannel bankChannel;

    private final Boolean needSignAgain;

    private final WhetherState mustCheck;

    private final Boolean isRightsTemplate;

    AgreementType(FileType fileType, String extension, String templateNo,
                  LoanStage loanStage, FlowChannel flowChannel,BankChannel bankChannel, Boolean needSignAgain, WhetherState mustCheck) {
        this.fileType = fileType;
        this.extension = extension;
        this.templateNo = templateNo;
        this.loanStage = loanStage;
        this.flowChannel = flowChannel;
        this.bankChannel = bankChannel;
        this.needSignAgain = needSignAgain;
        this.mustCheck = mustCheck;
        this.isRightsTemplate = false;
    }


    public static List<AgreementType> getAgreements(LoanStage loanStage, FlowChannel flowChannel,BankChannel bankChannel, WhetherState mustCheck) {
        return Arrays.stream(values()).filter(agreementType ->
                loanStage.equals(agreementType.loanStage)
                && (Objects.equals(bankChannel, agreementType.bankChannel) || Objects.isNull(agreementType.bankChannel))
                && (Objects.equals(flowChannel, agreementType.flowChannel) || Objects.isNull(agreementType.flowChannel))
                && mustCheck.equals(agreementType.mustCheck))
            .collect(Collectors.toList());

    }


    public static List<AgreementType> getAgreements(FlowChannel flowChannel,BankChannel bankChannel, LoanStage loanStage) {
        return Arrays.stream(values())
            .filter(agreementType ->
                (Objects.equals(flowChannel, agreementType.flowChannel)) &&
                (Objects.equals(bankChannel, agreementType.bankChannel) || Objects.isNull(agreementType.bankChannel))
                && agreementType.loanStage.equals(loanStage))
            .collect(Collectors.toList());
    }


    public static List<AgreementType> getRiskAgreements(FlowChannel flowChannel,BankChannel bankChannel) {
        return Arrays.stream(values())
            .filter(agreementType -> agreementType.loanStage == LoanStage.RISK && agreementType.getFileType() != FileType.FACE_AUTH_APPROVAL
                && flowChannel == agreementType.getFlowChannel() && bankChannel == agreementType.getBankChannel()
            ).toList();
    }

    public static AgreementType getAgreement(FileType fileType, LoanStage stage) {
        return Arrays.stream(values())
            .filter(agreementType -> agreementType.loanStage == stage && agreementType.getFileType() == fileType).findAny().orElseThrow();
    }


    public static AgreementType getAgreement(FileType fileType, BankChannel bankChannel) {
        return Arrays.stream(values())
            .filter(agreementType -> Objects.equals(fileType, agreementType.fileType)
                && (Objects.equals(bankChannel, agreementType.bankChannel) || Objects.isNull(agreementType.bankChannel))).findFirst().orElseThrow();
    }

    public WhetherState getMustCheck() {
        return mustCheck;
    }

    public FileType getFileType() {
        return fileType;
    }

    public String getExtension() {
        return extension;
    }

    public String getTemplateNo() {
        return templateNo;
    }

    public LoanStage getLoanStage() {
        return loanStage;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public Boolean getNeedSignAgain() {
        return needSignAgain;
    }

    public Boolean getRightsTemplate() {
        return isRightsTemplate;
    }
}
