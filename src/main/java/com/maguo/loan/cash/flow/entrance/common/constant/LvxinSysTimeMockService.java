package com.maguo.loan.cash.flow.entrance.common.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class LvxinSysTimeMockService {
    //  20250102135339
    @Value("${lvxin.sys.time}")
    private String rlSysTime;

    @Value("${lvxin.sysTime.mock}")
    private String rlSysTimeMock;
    public LocalDateTime isMockTime(LocalDateTime time) {
        return "true".equals(rlSysTimeMock) ? LocalDateTime.parse(rlSysTime, DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) : time;
    }

    public String getRlSysTime() {
        return rlSysTime;
    }

    public void setRlSysTime(String rlSysTime) {
        this.rlSysTime = rlSysTime;
    }

    public String getRlSysTimeMock() {
        return rlSysTimeMock;
    }

    public void setRlSysTimeMock(String rlSysTimeMock) {
        this.rlSysTimeMock = rlSysTimeMock;
    }
}
