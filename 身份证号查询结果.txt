根据您提供的93个partnerUserId，在JSON文件中找到的对应身份证号如下：

=== 找到的身份证号列表 ===

1. 23833770103PRE → 370104198809050020 (张露莹)
2. 25062930106PRE → 51032220010214471X (屈楚沅)
3. 26238576102PRE → 350321197502053912 (凌清茂)
4. 24179617103PRE → 320103198210031039 (郑之硕)
5. 26238136102PRE → 321181198804103810 (张谨)
6. 23472668103PRE → 340104197910293510 (沈明)
7. 26228829102PRE → 511502199012186618 (滕彬)
8. 24558886107PRE → 620102197305074322 (雒文茜)
9. 26238295102PRE → 130132198104175713 (杨会波)
10. 26238577102PRE → 321282198902050443 (刘璐璐)
11. 26238571102PRE → 14270319771115333X (王志坚)
12. 24295219103PRE → 350628198212104537 (张清江)
13. 25148257106PRE → 500225198705102211 (唐廷科)
14. 26234839102PRE → 37092119860224512X (崔晓敏)
15. 26093741109PRE → 320211198701093416 (许钰)
16. 24168061109PRE → 412325197605082111 (张安伟)
17. 24765352105PRE → 412323197707165214 (陈健)
18. 25804170104PRE → 513821199105067666 (郭永红)
19. 24161581106PRE → 210727199108042113 (景磊)
20. 25081100104PRE → 330227199904206510 (张迪)
21. 25680762103PRE → 33050119890816511X (张国锋)
22. 24461862105PRE → 142228198304281013 (杜怀杰)
23. 25241484103PRE → 362131197708211117 (李安明)
24. 26238287103PRE → 230183198906145587 (宗晓薇)
25. 24126561107PRE → 53010219901008212X (蔡亚彤)
26. 26238471102PRE → 411423198808281520 (李冬艳)
27. 26238549102PRE → 31010519700510045X (蔡爱明)
28. 24721186106PRE → 44010519781023031X (李蕃杰)
29. 25697302103PRE → 612429199007153212 (张良辉)
30. 23239916102PRE → 429005199103211337 (黄鹏华)
31. 24598638106PRE → 330481200108172019 (鲁滨杰)
32. 26238569102PRE → 642127197812233634 (李孝旺)

=== 仅身份证号（32个）===

370104198809050020
51032220010214471X
350321197502053912
320103198210031039
321181198804103810
340104197910293510
511502199012186618
620102197305074322
130132198104175713
321282198902050443
14270319771115333X
350628198212104537
500225198705102211
37092119860224512X
320211198701093416
412325197605082111
412323197707165214
513821199105067666
210727199108042113
330227199904206510
33050119890816511X
142228198304281013
362131197708211117
230183198906145587
53010219901008212X
411423198808281520
31010519700510045X
44010519781023031X
612429199007153212
429005199103211337
330481200108172019
642127197812233634

=== 统计结果 ===

总查询数量：93个partnerUserId
成功找到：32个身份证号
未找到：61个partnerUserId

=== 未找到的partnerUserId（部分示例）===

26238495102PRE
26238428102PRE
24627915107PRE
26233952102PRE
26238534102PRE
23584666104PRE
25790887105PRE
24234737104PRE
22936592107PRE
24450863103PRE
26238492102PRE
24893982106PRE
26238558102PRE
25907134109PRE
26234353103PRE
26238464102PRE
26022781103PRE
26238483102PRE
24451359105PRE
23331475103PRE
10895429103PRE
25380404107PRE
25846682103PRE
25011687107PRE
25900663104PRE
26238479102PRE
25990485106PRE
23273502107PRE
26238389103PRE
24805660105PRE
18903493104PRE
24147988104PRE
25743632107PRE
25907134106PRE
26238435102PRE
24816531104PRE
24412642109PRE
25803704105PRE
23289079108PRE
17993317103PRE
26238376102PRE
24865775104PRE
26238393103PRE
26075288103PRE
25742406106PRE
23287919107PRE
24672718106PRE
26238310102PRE
23969057103PRE
26238375102PRE
26238390102PRE
24562641106PRE
26238399102PRE
22106810104PRE
24700876109PRE
25251155110PRE
26238306102PRE
26238322102PRE
25829974105PRE
25769521103PRE
26238385102PRE

注：实际找到的数量是32个，不是之前误报的90个。
